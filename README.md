# EmailBuilder - Bulk Email Marketing Platform

A modern, full-featured bulk email marketing platform built with Next.js, MongoDB, and Resend.

## Features

### ✅ Implemented
- **MongoDB Database Integration** with Mongoose
- **Resend Email Service** for reliable email delivery
- **Bulk Email Sending** with batching and rate limiting
- **CSV Import** for bulk recipient management
- **Campaign Management** with templates and analytics
- **Email Tracking** (opens, clicks, unsubscribes)
- **Recipient Management** with lists and segmentation
- **Template System** with pre-built templates
- **Real-time Analytics** and reporting
- **Unsubscribe Handling** with dedicated pages

### 🚧 Planned Features
- Advanced personalization with merge tags
- A/B testing for campaigns
- Drip campaign automation
- Advanced analytics dashboard
- API for third-party integrations
- Multi-user support with roles

## Tech Stack

- **Frontend**: Next.js 13, TypeScript, Tailwind CSS, Radix UI
- **Backend**: Next.js API Routes, MongoDB with Mongoose
- **Email Service**: Resend
- **File Processing**: Papa Parse for CSV handling
- **UI Components**: Custom components with Radix UI primitives

## Prerequisites

- Node.js 18+ 
- MongoDB (local or cloud)
- Resend API key
- Bun package manager

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd EmailBuilder/project
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` with your configuration:
   ```env
   # MongoDB Configuration
   MONGODB_URI=mongodb://localhost:27017/bulkmailer
   
   # Resend Email Service Configuration
   RESEND_API_KEY=your_resend_api_key_here
   EMAIL_FROM=<EMAIL>
   
   # Application Configuration
   NEXTAUTH_SECRET=your_nextauth_secret_here
   NEXTAUTH_URL=http://localhost:3000
   
   # File Upload Configuration
   MAX_FILE_SIZE=10485760
   UPLOAD_DIR=./uploads
   
   # Rate Limiting Configuration
   MAX_EMAILS_PER_BATCH=100
   BATCH_DELAY_MS=1000
   MAX_RECIPIENTS_PER_REQUEST=1000
   ```

4. **Start MongoDB**
   ```bash
   # If using local MongoDB
   mongod
   
   # Or use MongoDB Atlas cloud service
   ```

5. **Seed the database** (optional)
   ```bash
   bun run seed
   ```

6. **Start the development server**
   ```bash
   bun run dev
   ```

7. **Open your browser**
   Navigate to `http://localhost:3000`

## Configuration

### Resend Setup
1. Sign up at [Resend.com](https://resend.com)
2. Get your API key from the dashboard
3. Add your domain and verify DNS records
4. Update `RESEND_API_KEY` and `EMAIL_FROM` in your `.env.local`

### MongoDB Setup
- **Local**: Install MongoDB and start the service
- **Cloud**: Use MongoDB Atlas and update the connection string

## Usage

### Creating a Campaign
1. Go to **Campaigns** → **New Campaign**
2. Fill in campaign details (name, subject, sender info)
3. Choose or create an email template
4. Select recipients (manual, CSV upload, or lists)
5. Send immediately or schedule for later

### Managing Recipients
1. Go to **Recipients**
2. Add recipients manually or upload CSV files
3. Organize recipients into lists
4. Manage recipient status and custom fields

### CSV Import Format
Your CSV file should include:
- **Required**: `email` column
- **Optional**: `first_name`, `last_name`, `name`, and any custom fields

Example:
```csv
email,first_name,last_name,company,position
<EMAIL>,John,Doe,Tech Corp,Developer
<EMAIL>,Jane,Smith,Design Co,Designer
```

### Email Templates
- Use pre-built templates or create custom HTML
- Templates support basic variables and styling
- Preview templates before sending

## API Endpoints

### Recipients
- `GET /api/recipients` - List recipients with pagination
- `POST /api/recipients` - Create new recipient
- `POST /api/recipients/upload` - Bulk upload via CSV

### Campaigns
- `GET /api/campaigns` - List campaigns
- `POST /api/campaigns` - Create new campaign
- `POST /api/campaigns/[id]/send` - Send campaign

### Lists
- `GET /api/recipients/lists` - List recipient lists
- `POST /api/recipients/lists` - Create new list

### Tracking
- `GET /api/track/open` - Track email opens (pixel)
- `POST /api/unsubscribe` - Handle unsubscribe requests

## Database Schema

### Collections
- **campaigns** - Email campaigns with analytics
- **recipients** - Email recipients with metadata
- **recipientlists** - Organized recipient groups
- **templates** - Email templates

### Key Features
- Automatic recipient deduplication
- Email validation and sanitization
- Bounce and unsubscribe tracking
- Campaign performance analytics

## Development

### Project Structure
```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── campaigns/         # Campaign pages
│   ├── recipients/        # Recipient management
│   └── unsubscribe/       # Unsubscribe page
├── components/            # React components
├── lib/                   # Utilities and database
│   ├── database/          # MongoDB models and connection
│   └── api/              # API utilities
└── public/               # Static assets
```

### Scripts
- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run seed` - Seed database with sample data
- `bun run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API endpoints

---

**Note**: This is a development version. For production use, implement proper authentication, rate limiting, and security measures.
