# Setup EmailBuilder Without Your Own Domain

You can use EmailBuilder immediately without owning a domain! Here's how:

## 🚀 Quick Setup (5 minutes)

### 1. Get Resend API Key (Free)
1. Go to [resend.com](https://resend.com)
2. Sign up with your email
3. Go to **API Keys** in the dashboard
4. Click **Create API Key**
5. Copy the API key (starts with `re_`)

### 2. Configure Environment
1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` with these values:
   ```env
   # MongoDB Configuration
   MONGODB_URI=mongodb://localhost:27017/bulkmailer
   
   # Resend Email Service Configuration
   RESEND_API_KEY=re_your_actual_api_key_here
   EMAIL_FROM=<EMAIL>
   
   # Application Configuration
   NEXTAUTH_SECRET=any_random_string_here
   NEXTAUTH_URL=http://localhost:3000
   
   # File Upload Configuration
   MAX_FILE_SIZE=********
   UPLOAD_DIR=./uploads
   
   # Rate Limiting Configuration
   MAX_EMAILS_PER_BATCH=100
   BATCH_DELAY_MS=1000
   MAX_RECIPIENTS_PER_REQUEST=1000
   ```

### 3. Install Dependencies
```bash
bun install
```

### 4. Start MongoDB
**Option A: Local MongoDB**
```bash
# Install MongoDB Community Edition
# Then start it:
mongod
```

**Option B: MongoDB Atlas (Cloud - Free)**
1. Go to [mongodb.com/atlas](https://mongodb.com/atlas)
2. Create free account
3. Create a free cluster
4. Get connection string
5. Update `MONGODB_URI` in `.env.local`

### 5. Seed Database (Optional)
```bash
bun run seed
```

### 6. Start the App
```bash
bun run dev
```

Visit `http://localhost:3000` 🎉

## 📧 Email Sending Limits

### With Resend Sandbox Domain (`<EMAIL>`):
- ✅ **100 emails/day** for free
- ✅ **No domain verification** required
- ✅ **Immediate setup**
- ✅ **Perfect for testing**

### Email Delivery:
- Emails will be delivered normally
- Recipients will see "from <EMAIL>"
- All tracking and analytics work perfectly

## 🧪 Testing Your Setup

1. **Create a Campaign:**
   - Go to Campaigns → New Campaign
   - Fill in details
   - Use your own email as recipient

2. **Send Test Email:**
   - Add yourself as recipient
   - Send the campaign
   - Check your inbox!

3. **Test CSV Upload:**
   - Go to Recipients
   - Upload a CSV with your email
   - Create campaign with that list

## 📈 Upgrading to Your Own Domain Later

When you're ready for production:

1. **Buy a domain** (any domain registrar)
2. **Add domain to Resend:**
   - Go to Resend dashboard
   - Add your domain
   - Add DNS records they provide
3. **Update environment:**
   ```env
   EMAIL_FROM=<EMAIL>
   ```

## 🔧 Troubleshooting

### "MongoDB connection failed"
- Make sure MongoDB is running: `mongod`
- Or use MongoDB Atlas cloud service

### "Resend API error"
- Check your API key is correct
- Make sure you're using `<EMAIL>` as EMAIL_FROM

### "Build errors"
- Run `bun install` again
- Check Node.js version (18+)

### "Port 3000 already in use"
- Stop other apps using port 3000
- Or change port: `bun run dev -- -p 3001`

## 💡 Pro Tips

1. **Use your real email** for testing to see actual delivery
2. **Check spam folder** if emails don't arrive
3. **Monitor Resend dashboard** for delivery stats
4. **Start with small batches** (10-50 emails) for testing

## 🎯 What You Can Do Right Now

- ✅ Send bulk emails to hundreds of recipients
- ✅ Upload CSV files with recipient lists
- ✅ Track email opens and clicks
- ✅ Manage campaigns and templates
- ✅ View analytics and reports
- ✅ Handle unsubscribes automatically

**No domain needed - start sending emails in 5 minutes!** 🚀
