import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { Campaign, Recipient } from '@/lib/database/models';
import { sendEmail } from '@/lib/api/mail';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    const campaignId = params.id;
    
    // Get campaign
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) {
      return NextResponse.json(
        { error: 'Campaign not found' },
        { status: 404 }
      );
    }
    
    // Check if campaign can be sent
    if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      return NextResponse.json(
        { error: 'Campaign cannot be sent. Current status: ' + campaign.status },
        { status: 400 }
      );
    }
    
    // Update campaign status to sending
    await Campaign.findByIdAndUpdate(campaignId, {
      status: 'sending',
      sentAt: new Date()
    });
    
    try {
      // Get all recipients for this campaign
      const allRecipients = new Set<string>();
      
      // Add individual recipients
      if (campaign.recipients && campaign.recipients.length > 0) {
        const individualRecipients = await Recipient.find({
          _id: { $in: campaign.recipients },
          status: 'active'
        }).select('email');
        
        for (const recipient of individualRecipients) {
          allRecipients.add(recipient.email);
        }
      }
      
      // Add recipients from lists
      if (campaign.recipientLists && campaign.recipientLists.length > 0) {
        const listRecipients = await Recipient.find({
          lists: { $in: campaign.recipientLists },
          status: 'active'
        }).select('email');
        
        for (const recipient of listRecipients) {
          allRecipients.add(recipient.email);
        }
      }
      
      const recipientEmails = Array.from(allRecipients);
      
      if (recipientEmails.length === 0) {
        await Campaign.findByIdAndUpdate(campaignId, {
          status: 'failed',
          failedCount: 1
        });
        
        return NextResponse.json(
          { error: 'No active recipients found for this campaign' },
          { status: 400 }
        );
      }
      
      // Send emails
      const result = await sendEmail({
        to: recipientEmails,
        subject: campaign.subject,
        html: campaign.content,
        from: `${campaign.senderName} <${campaign.senderEmail}>`,
        campaignId: campaignId
      });
      
      if (result.success) {
        // Update campaign status to sent
        await Campaign.findByIdAndUpdate(campaignId, {
          status: 'sent',
          sentCount: result.totalSent || 0,
          failedCount: result.totalFailed || 0,
          deliveredCount: result.totalSent || 0
        });
        
        return NextResponse.json({
          success: true,
          message: 'Campaign sent successfully',
          stats: {
            totalSent: result.totalSent,
            totalFailed: result.totalFailed,
            failedEmails: result.failedEmails
          }
        });
      } else {
        // Update campaign status to failed
        await Campaign.findByIdAndUpdate(campaignId, {
          status: 'failed',
          failedCount: recipientEmails.length
        });
        
        return NextResponse.json(
          { error: 'Failed to send campaign', details: result.error },
          { status: 500 }
        );
      }
      
    } catch (sendError) {
      // Update campaign status to failed
      await Campaign.findByIdAndUpdate(campaignId, {
        status: 'failed'
      });
      
      throw sendError;
    }
    
  } catch (error) {
    console.error('Error sending campaign:', error);
    return NextResponse.json(
      { error: 'Failed to send campaign' },
      { status: 500 }
    );
  }
}
