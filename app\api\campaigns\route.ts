import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { Campaign, Recipient, RecipientList } from '@/lib/database/models';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || '';
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    // Get campaigns with pagination
    const campaigns = await Campaign.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count
    const total = await Campaign.countDocuments(query);
    
    return NextResponse.json({
      campaigns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.subject || !data.content || !data.senderEmail) {
      return NextResponse.json(
        { error: 'Name, subject, content, and sender email are required' },
        { status: 400 }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.senderEmail)) {
      return NextResponse.json(
        { error: 'Invalid sender email format' },
        { status: 400 }
      );
    }
    
    // Calculate total recipients
    let totalRecipients = 0;
    const allRecipients = new Set<string>();
    
    // Add individual recipients
    if (data.recipients && data.recipients.length > 0) {
      for (const recipientId of data.recipients) {
        allRecipients.add(recipientId);
      }
    }
    
    // Add recipients from lists
    if (data.recipientLists && data.recipientLists.length > 0) {
      const listRecipients = await Recipient.find({
        lists: { $in: data.recipientLists },
        status: 'active'
      }).select('_id');
      
      for (const recipient of listRecipients) {
        allRecipients.add(recipient._id.toString());
      }
    }
    
    totalRecipients = allRecipients.size;
    
    if (totalRecipients === 0) {
      return NextResponse.json(
        { error: 'Campaign must have at least one recipient' },
        { status: 400 }
      );
    }
    
    // Create new campaign
    const campaign = await Campaign.create({
      name: data.name,
      subject: data.subject,
      content: data.content,
      templateId: data.templateId,
      senderName: data.senderName || 'Email Campaign',
      senderEmail: data.senderEmail,
      status: data.scheduledAt ? 'scheduled' : 'draft',
      scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : undefined,
      recipients: data.recipients || [],
      recipientLists: data.recipientLists || [],
      totalRecipients,
      settings: {
        trackOpens: data.trackOpens !== false,
        trackClicks: data.trackClicks !== false,
        unsubscribeLink: data.unsubscribeLink !== false
      },
      analytics: {
        opens: [],
        clicks: [],
        bounces: [],
        unsubscribes: []
      }
    });
    
    return NextResponse.json({
      success: true,
      campaign
    });
    
  } catch (error) {
    console.error('Error creating campaign:', error);
    return NextResponse.json(
      { error: 'Failed to create campaign' },
      { status: 500 }
    );
  }
}
