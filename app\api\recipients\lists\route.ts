import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { RecipientList, Recipient } from '@/lib/database/models';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    // Get lists with pagination
    const lists = await RecipientList.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count
    const total = await RecipientList.countDocuments(query);
    
    return NextResponse.json({
      lists,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching recipient lists:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recipient lists' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.name) {
      return NextResponse.json(
        { error: 'List name is required' },
        { status: 400 }
      );
    }
    
    // Check if list with same name already exists
    const existingList = await RecipientList.findOne({ name: data.name });
    if (existingList) {
      return NextResponse.json(
        { error: 'List with this name already exists' },
        { status: 409 }
      );
    }
    
    // Create new list
    const list = await RecipientList.create({
      name: data.name,
      description: data.description || '',
      tags: data.tags || [],
      settings: {
        allowDuplicates: data.allowDuplicates || false,
        autoCleanBounces: data.autoCleanBounces !== false,
        autoCleanUnsubscribes: data.autoCleanUnsubscribes !== false
      }
    });
    
    return NextResponse.json({
      success: true,
      list
    });
    
  } catch (error) {
    console.error('Error creating recipient list:', error);
    return NextResponse.json(
      { error: 'Failed to create recipient list' },
      { status: 500 }
    );
  }
}
