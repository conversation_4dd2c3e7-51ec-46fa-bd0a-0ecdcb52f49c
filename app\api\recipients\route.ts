import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { Recipient, RecipientList } from '@/lib/database/models';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const listId = searchParams.get('listId') || '';
    
    const skip = (page - 1) * limit;
    
    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (listId) {
      query.lists = listId;
    }
    
    // Get recipients with pagination
    const recipients = await Recipient.find(query)
      .populate('lists', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count
    const total = await Recipient.countDocuments(query);
    
    return NextResponse.json({
      recipients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching recipients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recipients' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }
    
    // Check if recipient already exists
    const existingRecipient = await Recipient.findOne({ email: data.email.toLowerCase() });
    if (existingRecipient) {
      return NextResponse.json(
        { error: 'Recipient with this email already exists' },
        { status: 409 }
      );
    }
    
    // Create new recipient
    const recipient = await Recipient.create({
      email: data.email.toLowerCase(),
      firstName: data.firstName || '',
      lastName: data.lastName || '',
      name: data.name || '',
      customFields: data.customFields || {},
      lists: data.lists || [],
      tags: data.tags || [],
      source: 'manual',
      status: 'active'
    });
    
    // Update list recipient counts
    if (data.lists && data.lists.length > 0) {
      await RecipientList.updateMany(
        { _id: { $in: data.lists } },
        { 
          $inc: { 
            recipientCount: 1,
            activeRecipientCount: 1
          }
        }
      );
    }
    
    return NextResponse.json({
      success: true,
      recipient
    });
    
  } catch (error) {
    console.error('Error creating recipient:', error);
    return NextResponse.json(
      { error: 'Failed to create recipient' },
      { status: 500 }
    );
  }
}
