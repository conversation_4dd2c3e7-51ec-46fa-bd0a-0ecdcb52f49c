import connectDB from "@/lib/database/connection";
import { Recipient, RecipientList } from "@/lib/database/models";
import { NextRequest, NextResponse } from "next/server";
import Papa from "papaparse";

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const listId = formData.get("listId") as string;
    const skipDuplicates = formData.get("skipDuplicates") === "true";

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Check file type
    const allowedTypes = ["text/csv", "application/vnd.ms-excel", "text/plain"];
    if (!allowedTypes.includes(file.type) && !file.name.endsWith(".csv")) {
      return NextResponse.json(
        { error: "Invalid file type. Please upload a CSV file." },
        { status: 400 }
      );
    }

    // Check file size (10MB limit)
    const maxSize = Number(process.env.MAX_FILE_SIZE) || 10485760; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 10MB." },
        { status: 400 }
      );
    }

    // Read and parse CSV
    const text = await file.text();
    const parseResult = Papa.parse(text, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.toLowerCase().trim(),
    });

    if (parseResult.errors.length > 0) {
      return NextResponse.json(
        { error: "Error parsing CSV file", details: parseResult.errors },
        { status: 400 }
      );
    }

    const data = parseResult.data as Record<string, any>[];

    if (data.length === 0) {
      return NextResponse.json(
        { error: "CSV file is empty or has no valid data" },
        { status: 400 }
      );
    }

    // Validate required email column
    const firstRow = data[0];
    const emailColumn = Object.keys(firstRow).find((key) =>
      ["email", "email_address", "e-mail", "mail"].includes(key.toLowerCase())
    );

    if (!emailColumn) {
      return NextResponse.json(
        {
          error:
            "CSV must contain an email column (email, email_address, e-mail, or mail)",
        },
        { status: 400 }
      );
    }

    // Process recipients
    const recipients = [];
    const errors = [];
    const duplicates = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const email = row[emailColumn]?.toString().toLowerCase().trim();

      if (!email) {
        errors.push({ row: i + 1, error: "Missing email address" });
        continue;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        errors.push({ row: i + 1, error: "Invalid email format", email });
        continue;
      }

      // Check for duplicates in database if skipDuplicates is true
      if (skipDuplicates) {
        const existingRecipient = await Recipient.findOne({ email });
        if (existingRecipient) {
          duplicates.push({ row: i + 1, email });
          continue;
        }
      }

      // Extract other fields
      const recipient = {
        email,
        firstName: row.first_name || row.firstname || row.fname || "",
        lastName: row.last_name || row.lastname || row.lname || "",
        name: row.name || row.full_name || row.fullname || "",
        customFields: {} as Record<string, any>,
        lists: listId ? [listId] : [],
        source: "csv_import",
        status: "active" as const,
      };

      // Add custom fields (any column that's not a standard field)
      const standardFields = [
        "email",
        "first_name",
        "firstname",
        "fname",
        "last_name",
        "lastname",
        "lname",
        "name",
        "full_name",
        "fullname",
      ];
      Object.keys(row).forEach((key) => {
        if (!standardFields.includes(key.toLowerCase()) && row[key]) {
          recipient.customFields[key] = row[key];
        }
      });

      recipients.push(recipient);
    }

    // Bulk insert recipients
    let insertedCount = 0;
    let updatedCount = 0;

    if (recipients.length > 0) {
      for (const recipient of recipients) {
        try {
          const existingRecipient = await Recipient.findOne({
            email: recipient.email,
          });

          if (existingRecipient) {
            // Update existing recipient
            await Recipient.findOneAndUpdate(
              { email: recipient.email },
              {
                $set: {
                  firstName: recipient.firstName || existingRecipient.firstName,
                  lastName: recipient.lastName || existingRecipient.lastName,
                  name: recipient.name || existingRecipient.name,
                  customFields: {
                    ...existingRecipient.customFields,
                    ...recipient.customFields,
                  },
                },
                $addToSet: { lists: { $each: recipient.lists } },
              }
            );
            updatedCount++;
          } else {
            // Create new recipient
            await Recipient.create(recipient);
            insertedCount++;
          }
        } catch (error) {
          console.error("Error processing recipient:", error);
          errors.push({
            email: recipient.email,
            error: "Database error during insert/update",
          });
        }
      }
    }

    // Update list recipient count if listId provided
    if (listId && insertedCount > 0) {
      await RecipientList.findByIdAndUpdate(listId, {
        $inc: {
          recipientCount: insertedCount,
          activeRecipientCount: insertedCount,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Recipients processed successfully",
      stats: {
        totalRows: data.length,
        inserted: insertedCount,
        updated: updatedCount,
        duplicatesSkipped: duplicates.length,
        errors: errors.length,
      },
      errors: errors.length > 0 ? errors : undefined,
      duplicates: duplicates.length > 0 ? duplicates : undefined,
    });
  } catch (error) {
    console.error("Error uploading recipients:", error);
    return NextResponse.json(
      { error: "Failed to process file" },
      { status: 500 }
    );
  }
}
