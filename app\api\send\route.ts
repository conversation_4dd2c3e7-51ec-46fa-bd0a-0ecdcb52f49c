import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/api/mail';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.to || !data.subject || !data.html) {
      return NextResponse.json(
        { error: 'To, subject, and html content are required' },
        { status: 400 }
      );
    }
    
    // Rate limiting check - this would typically use a more sophisticated approach
    // For example, checking a database for usage quotas
    const MAX_RECIPIENTS_PER_REQUEST = 1000;
    const recipients = Array.isArray(data.to) ? data.to : [data.to];
    
    if (recipients.length > MAX_RECIPIENTS_PER_REQUEST) {
      return NextResponse.json(
        { 
          error: `Exceeded maximum recipients per request (${MAX_RECIPIENTS_PER_REQUEST})`,
          details: 'Split your recipients into multiple smaller batches'
        },
        { status: 429 }
      );
    }
    
    // Send the email
    const result = await sendEmail({
      to: data.to,
      subject: data.subject,
      html: data.html,
      from: data.from,
      text: data.text,
    });
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Failed to send email', details: result.error },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      success: true,
      message: `Email${recipients.length > 1 ? 's' : ''} sent successfully`,
      count: recipients.length
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}