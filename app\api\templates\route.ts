import { NextRequest, NextResponse } from 'next/server';
import { getTemplates, createTemplate } from '@/lib/api/templates';

export async function GET(request: NextRequest) {
  try {
    const templates = await getTemplates();
    return NextResponse.json({ templates });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.content) {
      return NextResponse.json(
        { error: 'Name and content are required' },
        { status: 400 }
      );
    }
    
    const template = await createTemplate({
      name: data.name,
      description: data.description || '',
      content: data.content,
      thumbnail: data.thumbnail,
    });
    
    return NextResponse.json({ template }, { status: 201 });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}