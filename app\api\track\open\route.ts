import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { Campaign, Recipient } from '@/lib/database/models';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('c');
    const recipientEmail = searchParams.get('r');
    
    if (!campaignId || !recipientEmail) {
      // Return a 1x1 transparent pixel even if tracking fails
      return new NextResponse(
        Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
        {
          status: 200,
          headers: {
            'Content-Type': 'image/gif',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        }
      );
    }
    
    try {
      // Get user agent and IP address
      const userAgent = request.headers.get('user-agent') || '';
      const forwardedFor = request.headers.get('x-forwarded-for');
      const realIp = request.headers.get('x-real-ip');
      const ipAddress = forwardedFor?.split(',')[0] || realIp || 'unknown';
      
      // Find recipient
      const recipient = await Recipient.findOne({ email: recipientEmail });
      if (!recipient) {
        console.log('Recipient not found for email:', recipientEmail);
      } else {
        // Update recipient open count
        await Recipient.findByIdAndUpdate(recipient._id, {
          $inc: { emailsOpened: 1 }
        });
      }
      
      // Update campaign analytics
      await Campaign.findByIdAndUpdate(campaignId, {
        $inc: { openedCount: 1 },
        $push: {
          'analytics.opens': {
            recipientId: recipient?._id || 'unknown',
            timestamp: new Date(),
            userAgent,
            ipAddress
          }
        }
      });
      
      console.log('Email open tracked:', { campaignId, recipientEmail, ipAddress });
      
    } catch (trackingError) {
      console.error('Error tracking email open:', trackingError);
    }
    
    // Always return a 1x1 transparent pixel
    return new NextResponse(
      Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
      {
        status: 200,
        headers: {
          'Content-Type': 'image/gif',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
    
  } catch (error) {
    console.error('Error in open tracking:', error);
    
    // Always return a 1x1 transparent pixel even on error
    return new NextResponse(
      Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
      {
        status: 200,
        headers: {
          'Content-Type': 'image/gif',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}
