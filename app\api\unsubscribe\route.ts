import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import { Campaign, Recipient } from '@/lib/database/models';

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { campaignId, email } = await request.json();
    
    if (!campaignId || !email) {
      return NextResponse.json(
        { error: 'Campaign ID and email are required' },
        { status: 400 }
      );
    }
    
    // Find the recipient
    const recipient = await Recipient.findOne({ email: email.toLowerCase() });
    
    if (!recipient) {
      return NextResponse.json(
        { error: 'Recipient not found' },
        { status: 404 }
      );
    }
    
    // Check if already unsubscribed
    const alreadyUnsubscribed = recipient.status === 'unsubscribed';
    
    if (!alreadyUnsubscribed) {
      // Update recipient status to unsubscribed
      await Recipient.findByIdAndUpdate(recipient._id, {
        status: 'unsubscribed',
        unsubscribeDate: new Date()
      });
      
      // Add unsubscribe event to campaign analytics
      await Campaign.findByIdAndUpdate(campaignId, {
        $inc: { unsubscribedCount: 1 },
        $push: {
          'analytics.unsubscribes': {
            recipientId: recipient._id,
            timestamp: new Date()
          }
        }
      });
    }
    
    return NextResponse.json({
      success: true,
      message: alreadyUnsubscribed ? 'Already unsubscribed' : 'Successfully unsubscribed',
      alreadyUnsubscribed
    });
    
  } catch (error) {
    console.error('Error processing unsubscribe:', error);
    return NextResponse.json(
      { error: 'Failed to process unsubscribe request' },
      { status: 500 }
    );
  }
}
