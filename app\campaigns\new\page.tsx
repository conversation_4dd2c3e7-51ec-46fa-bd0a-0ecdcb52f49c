"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CampaignRecipients } from "@/components/campaigns/campaign-recipients";
import { ArrowLeft, Send } from "lucide-react";
import Link from "next/link";
import { ScrollArea } from "@/components/ui/scroll-area";

const templates = [
  {
    id: "template-1",
    name: "Newsletter",
    thumbnail: "https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
  {
    id: "template-2",
    name: "Product Announcement",
    thumbnail: "https://images.pexels.com/photos/821754/pexels-photo-821754.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
];

export default function NewCampaignPage() {
  const [activeTab, setActiveTab] = useState("details");
  const [selectedTemplate, setSelectedTemplate] = useState(templates[0]);
  const [customHtml, setCustomHtml] = useState("");

  return (
    <div className="container py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/campaigns">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">Create New Campaign</h2>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Campaign Details</TabsTrigger>
          <TabsTrigger value="template">Template</TabsTrigger>
          <TabsTrigger value="recipients">Recipients</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Details</CardTitle>
              <CardDescription>
                Enter the basic information for your email campaign
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Campaign Name</Label>
                <Input id="name" placeholder="e.g., Weekly Newsletter" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject Line</Label>
                <Input id="subject" placeholder="e.g., Your Weekly Update" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sender-name">Sender Name</Label>
                <Input id="sender-name" placeholder="e.g., Your Company" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sender-email">Sender Email Address</Label>
                <Input id="sender-email" type="email" placeholder="e.g., <EMAIL>" />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" asChild>
                <Link href="/campaigns">Cancel</Link>
              </Button>
              <Button onClick={() => setActiveTab("template")}>
                Next Step
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="template">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Choose Template</CardTitle>
                <CardDescription>
                  Select a template or paste your HTML
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="gallery">
                  <TabsList className="mb-4">
                    <TabsTrigger value="gallery">Template Gallery</TabsTrigger>
                    <TabsTrigger value="custom">Custom HTML</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="gallery">
                    <ScrollArea className="h-[400px] pr-4">
                      <div className="grid grid-cols-2 gap-4">
                        {templates.map((template) => (
                          <div
                            key={template.id}
                            className={`cursor-pointer rounded-lg border-2 overflow-hidden ${
                              selectedTemplate?.id === template.id ? "border-primary" : "border-border"
                            }`}
                            onClick={() => setSelectedTemplate(template)}
                          >
                            <div className="aspect-video relative">
                              <img
                                src={template.thumbnail}
                                alt={template.name}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            <div className="p-2 text-center">
                              <p className="font-medium">{template.name}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                  
                  <TabsContent value="custom">
                    <div className="space-y-4">
                      <Label htmlFor="custom-html">Paste Your HTML Template</Label>
                      <textarea
                        id="custom-html"
                        className="w-full h-[400px] font-mono text-sm p-4 rounded-lg border resize-none"
                        value={customHtml}
                        onChange={(e) => setCustomHtml(e.target.value)}
                        placeholder="<html>..."
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("details")}>
                  Back
                </Button>
                <Button onClick={() => setActiveTab("recipients")}>
                  Next Step
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  Preview how your email will look
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-[600px] border rounded-lg overflow-hidden bg-white">
                  <iframe
                    srcDoc={customHtml || "<h1>Template Preview</h1>"}
                    className="w-full h-full"
                    title="Template Preview"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="recipients">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Campaign Recipients</CardTitle>
                <CardDescription>
                  Select who will receive your email campaign
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CampaignRecipients />
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("template")}>
                  Back
                </Button>
                <Button>
                  <Send className="mr-2 h-4 w-4" />
                  Send Campaign
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  Preview how your email will look
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-[600px] border rounded-lg overflow-hidden bg-white">
                  <iframe
                    srcDoc={customHtml || "<h1>Template Preview</h1>"}
                    className="w-full h-full"
                    title="Template Preview"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}