import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ExternalLink, MoreHorizontal, PlusCircle } from "lucide-react";
import Link from "next/link";
// import { Progress } from "@/components/ui/progress";

const campaigns = [
  {
    id: "CAMP-001",
    name: "Weekly Newsletter",
    createdAt: "2023-04-12",
    status: "sent",
    recipients: 12532,
    openRate: 32,
    clickRate: 8,
  },
  {
    id: "CAMP-002",
    name: "Product Announcement",
    createdAt: "2023-04-10",
    status: "sent",
    recipients: 8421,
    openRate: 28,
    clickRate: 12,
  },
  {
    id: "CAMP-003",
    name: "Summer Sale",
    createdAt: "2023-04-03",
    status: "draft",
    recipients: 0,
    openRate: 0,
    clickRate: 0,
  },
  {
    id: "CAMP-004",
    name: "Customer Survey",
    createdAt: "2023-04-01",
    status: "scheduled",
    recipients: 4500,
    openRate: 0,
    clickRate: 0,
  },
  {
    id: "CAMP-005",
    name: "Product Launch",
    createdAt: "2023-03-28",
    status: "sent",
    recipients: 15240,
    openRate: 42,
    clickRate: 18,
  },
];

export default function CampaignsPage() {
  return (
    <div className="container py-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Email Campaigns</h2>
          <p className="text-muted-foreground">
            Create and manage your email campaigns
          </p>
        </div>
        <Button asChild>
          <Link href="/campaigns/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            New Campaign
          </Link>
        </Button>
      </div>

      <div className="mt-6 rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Recipients</TableHead>
              <TableHead>Open Rate</TableHead>
              <TableHead>Click Rate</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="w-[60px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {campaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell className="font-medium">{campaign.name}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      campaign.status === "sent"
                        ? "default"
                        : campaign.status === "scheduled"
                          ? "secondary"
                          : "outline"
                    }
                  >
                    {campaign.status}
                  </Badge>
                </TableCell>
                <TableCell>{campaign.recipients.toLocaleString()}</TableCell>
                <TableCell>
                  {campaign.status === "sent" ? (
                    <span className="font-medium">{campaign.openRate}%</span>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  {campaign.status === "sent" ? (
                    <span className="font-medium">{campaign.clickRate}%</span>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>{campaign.createdAt}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">More</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Link
                          href={`/campaigns/${campaign.id}`}
                          className="flex items-center"
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>Duplicate</DropdownMenuItem>
                      <DropdownMenuItem>Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
