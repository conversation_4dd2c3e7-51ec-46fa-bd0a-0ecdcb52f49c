import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { PlusCircle, MoreHorizontal, Upload, Download } from "lucide-react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const lists = [
  {
    id: "list-1",
    name: "Newsletter Subscribers",
    count: 1245,
    tags: ["newsletter"],
    lastUpdated: "2023-04-10",
  },
  {
    id: "list-2",
    name: "Active Customers",
    count: 856,
    tags: ["customers"],
    lastUpdated: "2023-04-05",
  },
  {
    id: "list-3",
    name: "Event Attendees",
    count: 326,
    tags: ["events"],
    lastUpdated: "2023-03-22",
  },
];

const recipients = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "John Doe",
    status: "active",
    lists: ["Newsletter Subscribers", "Active Customers"],
    added: "2023-04-01",
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "Jane Smith",
    status: "active",
    lists: ["Newsletter Subscribers"],
    added: "2023-03-28",
  },
  {
    id: "3",
    email: "<EMAIL>",
    name: "Bob Johnson",
    status: "unsubscribed",
    lists: ["Event Attendees"],
    added: "2023-03-15",
  },
  {
    id: "4",
    email: "<EMAIL>",
    name: "Sarah Williams",
    status: "active",
    lists: ["Newsletter Subscribers", "Active Customers", "Event Attendees"],
    added: "2023-03-10",
  },
  {
    id: "5",
    email: "<EMAIL>",
    name: "Mike Brown",
    status: "bounced",
    lists: ["Newsletter Subscribers"],
    added: "2023-03-05",
  },
];

export default function RecipientsPage() {
  return (
    <div className="container py-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Recipients</h2>
          <p className="text-muted-foreground">
            Manage your email recipients and lists
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Recipient
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="mt-6">
        <TabsList>
          <TabsTrigger value="all">All Recipients</TabsTrigger>
          <TabsTrigger value="lists">Lists</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>All Recipients</CardTitle>
              <CardDescription>
                View and manage all your email recipients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Lists</TableHead>
                    <TableHead>Added</TableHead>
                    <TableHead className="w-[60px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recipients.map((recipient) => (
                    <TableRow key={recipient.id}>
                      <TableCell className="font-medium">{recipient.email}</TableCell>
                      <TableCell>{recipient.name}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            recipient.status === "active" ? "default" : 
                            recipient.status === "unsubscribed" ? "secondary" : 
                            "destructive"
                          }
                        >
                          {recipient.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {recipient.lists.map((list) => (
                            <Badge key={list} variant="outline" className="text-xs">
                              {list}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{recipient.added}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">More</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>Edit</DropdownMenuItem>
                            <DropdownMenuItem>Add to List</DropdownMenuItem>
                            <DropdownMenuItem>Remove</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="lists" className="mt-4">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {lists.map((list) => (
              <Card key={list.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle>{list.name}</CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">More</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Edit</DropdownMenuItem>
                        <DropdownMenuItem>Duplicate</DropdownMenuItem>
                        <DropdownMenuItem>Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription>
                    {list.count} recipients
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Last Updated:</span>
                      <span>{list.lastUpdated}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {list.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex justify-between mt-4">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/recipients/lists/${list.id}`}>
                          View
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            <Card className="border-dashed">
              <CardHeader>
                <CardTitle>Create New List</CardTitle>
                <CardDescription>
                  Organize your recipients into a new list
                </CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center py-6">
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  New List
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}