"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { TemplateEditor } from "@/components/templates/template-editor";
import { TemplateSidebar } from "@/components/templates/template-sidebar";
import { TemplatePreview } from "@/components/templates/template-preview";
import { Save, Eye, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function TemplateBuilder() {
  const [templateName, setTemplateName] = useState("Untitled Template");
  const [activeTab, setActiveTab] = useState("editor");

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/templates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <Input
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            className="w-80 h-9 text-lg font-semibold"
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setActiveTab(activeTab === "editor" ? "preview" : "editor")}
          >
            <Eye className="mr-2 h-4 w-4" />
            {activeTab === "editor" ? "Preview" : "Edit"}
          </Button>
          <Button>
            <Save className="mr-2 h-4 w-4" />
            Save Template
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="editor">Editor</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        <TabsContent value="editor" className="space-y-4">
          <div className="grid grid-cols-12 gap-6">
            <Card className="col-span-3 overflow-auto h-[calc(100vh-220px)]">
              <TemplateSidebar />
            </Card>
            <div className="col-span-9 overflow-auto h-[calc(100vh-220px)]">
              <TemplateEditor />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="preview">
          <div className="flex justify-center w-full">
            <div className="max-w-2xl w-full">
              <TemplatePreview />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}