"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AddHTMLTemplatePage() {
  const [html, setHtml] = useState("");
  
  return (
    <div className="container py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/templates">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">Add HTML Template</h2>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Template Details</CardTitle>
            <CardDescription>
              Enter your HTML template code and preview it
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name</Label>
              <Input id="name" placeholder="e.g., Newsletter Template" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input id="description" placeholder="Brief description of the template" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="html">HTML Code</Label>
              <Textarea 
                id="html" 
                placeholder="Paste your HTML template code here"
                className="min-h-[400px] font-mono"
                value={html}
                onChange={(e) => setHtml(e.target.value)}
              />
            </div>
            <Button className="w-full">
              Save Template
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>
              See how your template looks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="w-full h-[600px] border rounded-lg overflow-hidden bg-white">
              <iframe
                srcDoc={html}
                className="w-full h-full"
                title="Template Preview"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}