"use client";

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle } from 'lucide-react';

export default function UnsubscribePage() {
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'already'>('loading');
  const [email, setEmail] = useState<string>('');

  useEffect(() => {
    const campaignId = searchParams.get('c');
    const recipientEmail = searchParams.get('r');

    if (!campaignId || !recipientEmail) {
      setStatus('error');
      return;
    }

    setEmail(decodeURIComponent(recipientEmail));
    handleUnsubscribe(campaignId, recipientEmail);
  }, [searchParams]);

  const handleUnsubscribe = async (campaignId: string, recipientEmail: string) => {
    try {
      const response = await fetch('/api/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaignId,
          email: recipientEmail
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setStatus(result.alreadyUnsubscribed ? 'already' : 'success');
      } else {
        setStatus('error');
      }
    } catch (error) {
      console.error('Unsubscribe error:', error);
      setStatus('error');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {status === 'success' && <CheckCircle className="h-6 w-6 text-green-600" />}
            {status === 'already' && <CheckCircle className="h-6 w-6 text-blue-600" />}
            {status === 'error' && <XCircle className="h-6 w-6 text-red-600" />}
            {status === 'loading' && <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />}
            
            {status === 'loading' && 'Processing...'}
            {status === 'success' && 'Unsubscribed Successfully'}
            {status === 'already' && 'Already Unsubscribed'}
            {status === 'error' && 'Unsubscribe Failed'}
          </CardTitle>
          
          <CardDescription>
            {status === 'loading' && 'Please wait while we process your unsubscribe request.'}
            {status === 'success' && `${email} has been successfully unsubscribed from our mailing list.`}
            {status === 'already' && `${email} was already unsubscribed from our mailing list.`}
            {status === 'error' && 'We encountered an error processing your unsubscribe request. Please try again or contact support.'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          {status === 'success' && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                You will no longer receive emails from this campaign. If you continue to receive emails, 
                please contact our support team.
              </p>
              <p className="text-sm text-muted-foreground">
                Note: You may still receive transactional emails related to your account or purchases.
              </p>
            </div>
          )}
          
          {status === 'already' && (
            <p className="text-sm text-muted-foreground">
              Your email address was already marked as unsubscribed. If you continue to receive emails, 
              please contact our support team.
            </p>
          )}
          
          {status === 'error' && (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                If this problem persists, please contact our support team with the following information:
              </p>
              <div className="text-xs bg-gray-100 p-2 rounded font-mono">
                Email: {email}<br/>
                Campaign: {searchParams.get('c')}<br/>
                Time: {new Date().toISOString()}
              </div>
              <Button 
                onClick={() => window.location.reload()} 
                variant="outline"
                className="w-full"
              >
                Try Again
              </Button>
            </div>
          )}
          
          <div className="pt-4 border-t">
            <p className="text-xs text-muted-foreground">
              © 2024 EmailBuilder. All rights reserved.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
