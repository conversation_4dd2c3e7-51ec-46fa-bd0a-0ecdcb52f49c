import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

export function CampaignPreview() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Campaign Details</h3>
          <dl className="space-y-2">
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Name:</dt>
              <dd className="w-2/3 font-medium">Weekly Newsletter</dd>
            </div>
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Subject:</dt>
              <dd className="w-2/3 font-medium">Your Weekly Update from Our Company</dd>
            </div>
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">From:</dt>
              <dd className="w-2/3">Our Company &lt;<EMAIL>&gt;</dd>
            </div>
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Recipients:</dt>
              <dd className="w-2/3">3 recipients</dd>
            </div>
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Schedule:</dt>
              <dd className="w-2/3">Send Immediately</dd>
            </div>
          </dl>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Email Template</h3>
          <dl className="space-y-2">
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Template:</dt>
              <dd className="w-2/3 font-medium">Newsletter</dd>
            </div>
            <div className="flex">
              <dt className="w-1/3 text-muted-foreground">Preheader:</dt>
              <dd className="w-2/3">Check out our latest updates and offers</dd>
            </div>
          </dl>
        </div>
      </div>
      
      <Separator />
      
      <div>
        <h3 className="text-lg font-medium mb-4">Preview</h3>
        <Tabs defaultValue="desktop">
          <TabsList>
            <TabsTrigger value="desktop">Desktop</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>
          
          <TabsContent value="desktop">
            <div className="rounded-lg border overflow-hidden">
              <div className="bg-secondary p-3 flex items-center">
                <div className="bg-background rounded-full h-3 w-3 mr-2"></div>
                <div className="bg-background rounded-full h-3 w-3 mr-2"></div>
                <div className="bg-background rounded-full h-3 w-3 mr-2"></div>
                <div className="flex-1 bg-background rounded h-6 mx-2"></div>
              </div>
              <ScrollArea className="h-[400px]">
                <div className="p-4 bg-muted">
                  <div className="max-w-3xl mx-auto bg-white dark:bg-black rounded shadow-sm p-6">
                    <div className="text-center">
                      <h1 className="text-2xl font-bold mb-2">Welcome to Our Newsletter</h1>
                      <p className="text-muted-foreground">Stay updated with our latest news and offers</p>
                    </div>
                    
                    <div className="my-6 aspect-[2/1] bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                      <p className="text-muted-foreground">Header Image</p>
                    </div>
                    
                    <div className="mb-6">
                      <h2 className="text-xl font-semibold mb-2">Latest Updates</h2>
                      <p className="mb-4">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.
                      </p>
                    </div>
                    
                    <div className="text-center mb-6">
                      <Badge variant="secondary" className="mb-2">Special Offer</Badge>
                      <h3 className="text-lg font-semibold mb-2">25% Off All Products</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Use code NEWSLETTER25 at checkout
                      </p>
                      <div className="inline-block bg-primary text-primary-foreground px-4 py-2 rounded">
                        Shop Now
                      </div>
                    </div>
                    
                    <div className="text-center pt-6 border-t text-sm text-muted-foreground">
                      <p>© 2023 Your Company. All rights reserved.</p>
                      <div className="mt-2">
                        <a href="#" className="text-primary underline">Unsubscribe</a> • 
                        <a href="#" className="text-primary underline ml-2">Privacy Policy</a>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
          
          <TabsContent value="mobile">
            <div className="flex justify-center">
              <div className="w-[320px] h-[500px] border-8 border-gray-300 rounded-[36px] overflow-hidden">
                <div className="bg-secondary p-2 flex items-center">
                  <div className="bg-background rounded-full h-2 w-2 mr-1"></div>
                  <div className="bg-background rounded-full h-2 w-2 mr-1"></div>
                  <div className="bg-background rounded-full h-2 w-2 mr-1"></div>
                </div>
                <ScrollArea className="h-[450px]">
                  <div className="p-2 bg-muted">
                    <div className="bg-white dark:bg-black rounded shadow-sm p-4">
                      <div className="text-center">
                        <h1 className="text-xl font-bold mb-2">Welcome to Our Newsletter</h1>
                        <p className="text-xs text-muted-foreground">Stay updated with our latest news</p>
                      </div>
                      
                      <div className="my-4 aspect-[2/1] bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center">
                        <p className="text-xs text-muted-foreground">Header Image</p>
                      </div>
                      
                      <div className="mb-4">
                        <h2 className="text-lg font-semibold mb-2">Latest Updates</h2>
                        <p className="text-sm mb-4">
                          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim.
                        </p>
                      </div>
                      
                      <div className="text-center mb-4">
                        <Badge variant="secondary" className="mb-2 text-xs">Special Offer</Badge>
                        <h3 className="text-base font-semibold mb-2">25% Off All Products</h3>
                        <p className="text-xs text-muted-foreground mb-3">
                          Use code NEWSLETTER25 at checkout
                        </p>
                        <div className="inline-block bg-primary text-primary-foreground px-3 py-1 text-sm rounded">
                          Shop Now
                        </div>
                      </div>
                      
                      <div className="text-center pt-4 border-t text-xs text-muted-foreground">
                        <p>© 2023 Your Company. All rights reserved.</p>
                        <div className="mt-2">
                          <a href="#" className="text-primary underline">Unsubscribe</a> • 
                          <a href="#" className="text-primary underline ml-2">Privacy Policy</a>
                        </div>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}