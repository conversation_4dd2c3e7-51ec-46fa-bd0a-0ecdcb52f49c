"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Upload, Users, X, Plus, Check } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Card, 
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";

type Recipient = {
  id: string;
  email: string;
  name: string;
  status: string;
};

export function CampaignRecipients() {
  const [recipients, setRecipients] = useState<Recipient[]>([
    { id: "1", email: "<EMAIL>", name: "<PERSON>", status: "active" },
    { id: "2", email: "<EMAIL>", name: "<PERSON>", status: "active" },
    { id: "3", email: "<EMAIL>", name: "Bob Johnson", status: "active" },
  ]);
  const [newEmail, setNewEmail] = useState("");
  const [newName, setNewName] = useState("");

  const addRecipient = () => {
    if (newEmail) {
      setRecipients([
        ...recipients,
        {
          id: Date.now().toString(),
          email: newEmail,
          name: newName || newEmail.split("@")[0],
          status: "active"
        }
      ]);
      setNewEmail("");
      setNewName("");
    }
  };

  const removeRecipient = (id: string) => {
    setRecipients(recipients.filter(r => r.id !== id));
  };

  return (
    <Tabs defaultValue="manual">
      <TabsList>
        <TabsTrigger value="manual">Add Manually</TabsTrigger>
        <TabsTrigger value="upload">Upload File</TabsTrigger>
        <TabsTrigger value="list">Select List</TabsTrigger>
      </TabsList>
      
      <TabsContent value="manual" className="space-y-6">
        <div className="flex gap-4">
          <div className="flex-1 space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input 
              id="email" 
              placeholder="<EMAIL>" 
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
            />
          </div>
          <div className="flex-1 space-y-2">
            <Label htmlFor="name">Name (Optional)</Label>
            <Input 
              id="name" 
              placeholder="John Doe"
              value={newName}
              onChange={(e) => setNewName(e.target.value)} 
            />
          </div>
          <div className="flex items-end">
            <Button onClick={addRecipient}>
              <Plus className="mr-2 h-4 w-4" />
              Add
            </Button>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Recipients ({recipients.length})</h3>
          <Card>
            <ScrollArea className="h-72">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead className="w-[60px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recipients.map((recipient) => (
                    <TableRow key={recipient.id}>
                      <TableCell>{recipient.email}</TableCell>
                      <TableCell>{recipient.name}</TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => removeRecipient(recipient.id)}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </Card>
        </div>
      </TabsContent>
      
      <TabsContent value="upload">
        <Card>
          <CardHeader>
            <CardTitle>Upload Recipients</CardTitle>
            <CardDescription>
              Upload a CSV or Excel file with your recipients.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center py-6">
            <div className="rounded-lg border border-dashed border-border p-10 text-center">
              <Upload className="mx-auto h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="mb-2 font-medium">Drag and drop your file</h3>
              <p className="text-sm text-muted-foreground mb-4">
                CSV, Excel, or tab-delimited text files supported
              </p>
              <Button>
                Browse Files
              </Button>
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>Your file should include at minimum an email column.</p>
              <p>Optional columns: first_name, last_name, name, etc.</p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="list">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Select Recipient List</CardTitle>
              <CardDescription>
                Choose from your saved recipient lists.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { id: "1", name: "Newsletter Subscribers", count: 1245 },
                  { id: "2", name: "Active Customers", count: 856 },
                  { id: "3", name: "Event Attendees", count: 326 },
                ].map((list) => (
                  <div 
                    key={list.id} 
                    className="flex items-center justify-between p-4 border rounded-md hover:border-primary cursor-pointer"
                  >
                    <div>
                      <h4 className="font-medium">{list.name}</h4>
                      <p className="text-sm text-muted-foreground">{list.count} recipients</p>
                    </div>
                    <Button variant="ghost" size="icon">
                      <Check className="h-5 w-5" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                <Users className="mr-2 h-4 w-4" />
                Create New List
              </Button>
            </CardFooter>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  );
}