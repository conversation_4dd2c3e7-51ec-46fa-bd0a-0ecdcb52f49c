"use client";

import { AreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveC<PERSON><PERSON>, <PERSON> } from "recharts";

const data = [
  { name: "Jan 1", openRate: 40, clickRate: 24 },
  { name: "Jan 8", openRate: 30, clickRate: 13 },
  { name: "Jan 15", openRate: 20, clickRate: 8 },
  { name: "Jan 22", openRate: 27, clickRate: 11 },
  { name: "Jan 29", openRate: 18, clickRate: 7 },
  { name: "Feb 5", openRate: 23, clickRate: 12 },
  { name: "Feb 12", openRate: 34, clickRate: 16 },
];

export function Overview() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="name" 
            className="text-xs text-muted-foreground"
          />
          <YAxis 
            className="text-xs text-muted-foreground"
            tickFormatter={(value) => `${value}%`}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: "hsl(var(--background))",
              borderColor: "hsl(var(--border))",
              borderRadius: "var(--radius)",
            }}
            formatter={(value) => [`${value}%`]}
            labelStyle={{
              fontWeight: "bold",
              color: "hsl(var(--foreground))",
            }}
          />
          <Legend />
          <Area
            type="monotone"
            dataKey="openRate"
            name="Open Rate"
            stroke="hsl(var(--chart-1))"
            fill="hsl(var(--chart-1) / 0.3)"
          />
          <Area
            type="monotone"
            dataKey="clickRate"
            name="Click Rate"
            stroke="hsl(var(--chart-2))"
            fill="hsl(var(--chart-2) / 0.3)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}