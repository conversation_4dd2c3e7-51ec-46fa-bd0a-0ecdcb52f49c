import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

const campaigns = [
  {
    id: "CAMP-001",
    name: "Weekly Newsletter",
    date: "2023-04-12",
    status: "sent",
    stats: "12,532 sent • 32% open rate",
  },
  {
    id: "CAMP-002",
    name: "Product Announcement",
    date: "2023-04-10",
    status: "sent",
    stats: "8,421 sent • 28% open rate",
  },
  {
    id: "CAMP-003",
    name: "Summer Sale",
    date: "2023-04-03",
    status: "draft",
    stats: "Not sent yet",
  },
  {
    id: "CAMP-004",
    name: "Customer Survey",
    date: "2023-04-01",
    status: "scheduled",
    stats: "Scheduled for April 15",
  },
];

export function RecentCampaigns() {
  return (
    <div className="space-y-8">
      {campaigns.map((campaign) => (
        <div key={campaign.id} className="flex items-center">
          <Avatar className="h-9 w-9">
            <AvatarImage src={`https://avatar.vercel.sh/${campaign.id}`} alt={campaign.name} />
            <AvatarFallback>{campaign.name.slice(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{campaign.name}</p>
            <p className="text-sm text-muted-foreground">
              {campaign.stats}
            </p>
          </div>
          <div className="ml-auto">
            <Badge 
              variant={campaign.status === "sent" ? "default" : 
                     campaign.status === "scheduled" ? "secondary" : 
                     "outline"}
            >
              {campaign.status}
            </Badge>
          </div>
        </div>
      ))}
    </div>
  );
}