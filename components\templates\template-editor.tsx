"use client";

import { useState, DragEvent } from "react";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, X, Grip, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";

type TemplateComponent = {
  id: string;
  type: string;
  content?: string;
  children?: TemplateComponent[];
};

export function TemplateEditor() {
  const [components, setComponents] = useState<TemplateComponent[]>([]);
  const [activeDropZone, setActiveDropZone] = useState<string | null>(null);

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const componentType = e.dataTransfer.getData("componentType");
    
    if (componentType) {
      const newComponent: TemplateComponent = {
        id: `comp-${Date.now()}`,
        type: componentType,
        content: getDefaultContent(componentType),
      };
      
      setComponents([...components, newComponent]);
    }
    
    setActiveDropZone(null);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setActiveDropZone("main");
  };

  const handleDragLeave = () => {
    setActiveDropZone(null);
  };

  const removeComponent = (id: string) => {
    setComponents(components.filter(comp => comp.id !== id));
  };

  const getDefaultContent = (type: string): string => {
    switch (type) {
      case "text":
        return "Add your text here";
      case "button":
        return "Click Me";
      case "link":
        return "Click Here";
      default:
        return "";
    }
  };

  return (
    <ScrollArea className="h-full rounded-md border">
      <div 
        className="min-h-[800px] p-6 bg-gray-50 dark:bg-gray-900"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {components.length === 0 ? (
          <div 
            className={`border-2 border-dashed rounded-lg p-12 flex flex-col items-center justify-center min-h-[400px] transition-colors ${
              activeDropZone === "main" ? "border-primary bg-primary/5" : "border-border"
            }`}
          >
            <p className="text-muted-foreground mb-4">Drag and drop components here</p>
            <Button variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Add Component
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {components.map((component) => (
              <ComponentRenderer 
                key={component.id}
                component={component}
                onRemove={() => removeComponent(component.id)}
              />
            ))}
            <div 
              className={`border-2 border-dashed rounded-lg p-4 flex items-center justify-center transition-colors ${
                activeDropZone === "main" ? "border-primary bg-primary/5" : "border-border"
              }`}
            >
              <Button variant="ghost" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Component
              </Button>
            </div>
          </div>
        )}
      </div>
    </ScrollArea>
  );
}

function ComponentRenderer({ 
  component, 
  onRemove 
}: { 
  component: TemplateComponent;
  onRemove: () => void;
}) {
  return (
    <Card className="relative group">
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
        <Button variant="ghost" size="icon" className="h-6 w-6">
          <Settings className="h-3 w-3" />
        </Button>
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onRemove}>
          <X className="h-3 w-3" />
        </Button>
      </div>
      <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button variant="ghost" size="icon" className="h-6 w-6 cursor-grab">
          <Grip className="h-3 w-3" />
        </Button>
      </div>
      <div className="p-4 pt-8">
        {component.type === "text" && (
          <p contentEditable suppressContentEditableWarning className="focus:outline-none">
            {component.content}
          </p>
        )}
        {component.type === "image" && (
          <div className="aspect-video bg-muted flex items-center justify-center">
            <p className="text-muted-foreground text-sm">Image Placeholder</p>
          </div>
        )}
        {component.type === "button" && (
          <Button contentEditable suppressContentEditableWarning>
            {component.content}
          </Button>
        )}
        {component.type === "link" && (
          <a href="#" className="text-primary underline" contentEditable suppressContentEditableWarning>
            {component.content}
          </a>
        )}
        {component.type === "single-column" && (
          <div className="bg-muted/30 p-4 rounded border">
            <p className="text-center text-sm text-muted-foreground">Single Column Layout</p>
          </div>
        )}
        {component.type === "two-column" && (
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-muted/30 p-4 rounded border">
              <p className="text-center text-sm text-muted-foreground">Column 1</p>
            </div>
            <div className="bg-muted/30 p-4 rounded border">
              <p className="text-center text-sm text-muted-foreground">Column 2</p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}