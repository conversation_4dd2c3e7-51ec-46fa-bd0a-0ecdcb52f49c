"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function TemplatePreview() {
  return (
    <div className="space-y-4">
      <Card className="p-4">
        <Tabs defaultValue="desktop">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="desktop">Desktop</TabsTrigger>
              <TabsTrigger value="mobile">Mobile</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                Test Send
              </Button>
            </div>
          </div>

          <TabsContent value="desktop">
            <ScrollArea className="h-[calc(100vh-280px)] bg-gray-50 dark:bg-gray-900 rounded-md border p-4">
              <div className="mx-auto max-w-2xl">
                <div className="bg-white dark:bg-black rounded-md shadow-sm p-6 space-y-6">
                  <div className="text-center">
                    <h1 className="text-2xl font-bold mb-2">Welcome to Our Newsletter</h1>
                    <p className="text-muted-foreground">Stay updated with our latest news and offers</p>
                  </div>
                  
                  <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">Header Image</p>
                  </div>
                  
                  <div>
                    <h2 className="text-xl font-semibold mb-2">Latest Updates</h2>
                    <p className="mb-4">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.
                    </p>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="aspect-video bg-muted rounded-md mb-2"></div>
                        <h3 className="text-base font-medium">Product Highlight</h3>
                        <p className="text-sm text-muted-foreground">
                          Check out our latest product launch
                        </p>
                      </div>
                      <div>
                        <div className="aspect-video bg-muted rounded-md mb-2"></div>
                        <h3 className="text-base font-medium">Customer Story</h3>
                        <p className="text-sm text-muted-foreground">
                          See how our customers succeed
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <Button>Learn More</Button>
                  </div>
                  
                  <div className="text-center pt-6 border-t text-sm text-muted-foreground">
                    <p>© 2023 Your Company. All rights reserved.</p>
                    <div className="mt-2">
                      <a href="#" className="text-primary underline">Unsubscribe</a> • 
                      <a href="#" className="text-primary underline ml-2">Privacy Policy</a>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="mobile">
            <div className="flex justify-center">
              <div className="w-[375px] h-[600px] border-8 border-gray-300 rounded-[36px] overflow-hidden bg-white dark:bg-black">
                <ScrollArea className="h-full bg-gray-50 dark:bg-gray-900">
                  <div className="p-4 space-y-4">
                    <div className="text-center">
                      <h1 className="text-xl font-bold mb-2">Welcome to Our Newsletter</h1>
                      <p className="text-xs text-muted-foreground">Stay updated with our latest news and offers</p>
                    </div>
                    
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                      <p className="text-xs text-muted-foreground">Header Image</p>
                    </div>
                    
                    <div>
                      <h2 className="text-lg font-semibold mb-2">Latest Updates</h2>
                      <p className="text-sm mb-4">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat.
                      </p>
                      <div className="space-y-4">
                        <div>
                          <div className="aspect-video bg-muted rounded-md mb-2"></div>
                          <h3 className="text-sm font-medium">Product Highlight</h3>
                          <p className="text-xs text-muted-foreground">
                            Check out our latest product launch
                          </p>
                        </div>
                        <div>
                          <div className="aspect-video bg-muted rounded-md mb-2"></div>
                          <h3 className="text-sm font-medium">Customer Story</h3>
                          <p className="text-xs text-muted-foreground">
                            See how our customers succeed
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <Button size="sm">Learn More</Button>
                    </div>
                    
                    <div className="text-center pt-4 border-t text-xs text-muted-foreground">
                      <p>© 2023 Your Company. All rights reserved.</p>
                      <div className="mt-2">
                        <a href="#" className="text-primary underline">Unsubscribe</a> • 
                        <a href="#" className="text-primary underline ml-2">Privacy Policy</a>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}