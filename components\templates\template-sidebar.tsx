"use client";

import { DragE<PERSON> } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Image, Type, Columns, Grid2X2, Link2, Donut as ButtonIcon } from "lucide-react";

export function TemplateSidebar() {
  const handleDragStart = (e: DragEvent<HTMLDivElement>, componentType: string) => {
    e.dataTransfer.setData("componentType", componentType);
  };

  return (
    <ScrollArea className="h-full p-4">
      <Tabs defaultValue="components">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="styles">Styles</TabsTrigger>
        </TabsList>
        <TabsContent value="components">
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="layout" defaultChecked>
              <AccordionTrigger>Layout</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-2">
                  <ComponentItem
                    icon={<Columns className="h-4 w-4" />}
                    title="Single Column"
                    onDragStart={(e) => handleDragStart(e, "single-column")}
                  />
                  <ComponentItem
                    icon={<Grid2X2 className="h-4 w-4" />}
                    title="Two Column"
                    onDragStart={(e) => handleDragStart(e, "two-column")}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="content">
              <AccordionTrigger>Content</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-2">
                  <ComponentItem
                    icon={<Type className="h-4 w-4" />}
                    title="Text"
                    onDragStart={(e) => handleDragStart(e, "text")}
                  />
                  <ComponentItem
                    icon={<Image className="h-4 w-4" />}
                    title="Image"
                    onDragStart={(e) => handleDragStart(e, "image")}
                  />
                  <ComponentItem
                    icon={<ButtonIcon className="h-4 w-4" />}
                    title="Button"
                    onDragStart={(e) => handleDragStart(e, "button")}
                  />
                  <ComponentItem
                    icon={<Link2 className="h-4 w-4" />}
                    title="Link"
                    onDragStart={(e) => handleDragStart(e, "link")}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </TabsContent>
        <TabsContent value="styles">
          <p className="text-sm text-muted-foreground">
            Apply styles to selected components.
          </p>
          <div className="space-y-4 mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Colors</label>
              <div className="grid grid-cols-5 gap-2">
                {["bg-primary", "bg-secondary", "bg-accent", "bg-muted", "bg-white"].map((color) => (
                  <div 
                    key={color}
                    className={`${color} h-6 w-6 rounded-full border`}
                  />
                ))}
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Typography</label>
              <div className="space-y-1">
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Type className="mr-2 h-4 w-4" /> Heading
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Type className="mr-2 h-4 w-4" /> Subheading
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Type className="mr-2 h-4 w-4" /> Body
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </ScrollArea>
  );
}

function ComponentItem({ icon, title, onDragStart }: { 
  icon: React.ReactNode;
  title: string;
  onDragStart: (e: DragEvent<HTMLDivElement>) => void;
}) {
  return (
    <Card
      draggable
      onDragStart={onDragStart}
      className="cursor-grab hover:border-primary transition-colors"
    >
      <CardContent className="p-3 flex flex-col items-center gap-1">
        {icon}
        <span className="text-xs font-medium">{title}</span>
      </CardContent>
    </Card>
  );
}