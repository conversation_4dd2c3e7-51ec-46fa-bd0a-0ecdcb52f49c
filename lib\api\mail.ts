import nodemailer from 'nodemailer';

interface SendEmailProps {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  text?: string;
}

export async function sendEmail({
  to,
  subject,
  html,
  from = process.env.EMAIL_FROM || '<EMAIL>',
  text,
}: SendEmailProps) {
  const transport = nodemailer.createTransport({
    host: process.env.EMAIL_SERVER_HOST || 'smtp.resend.com',
    port: Number(process.env.EMAIL_SERVER_PORT || 587),
    secure: process.env.EMAIL_SERVER_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_SERVER_USER || 'resend',
      pass: process.env.EMAIL_SERVER_PASSWORD || process.env.RESEND_API_KEY,
    },
  });

  try {
    const recipients = Array.isArray(to) ? to : [to];
    
    // Apply rate limiting - this is a simple example
    // In a production environment, use a proper rate limiting solution
    const MAX_EMAILS_PER_BATCH = 100;
    const BATCH_DELAY_MS = 1000;
    
    // Split recipients into batches
    const batches = [];
    for (let i = 0; i < recipients.length; i += MAX_EMAILS_PER_BATCH) {
      batches.push(recipients.slice(i, i + MAX_EMAILS_PER_BATCH));
    }
    
    // Send emails in batches
    const results = [];
    for (const batch of batches) {
      const batchPromises = batch.map(recipient => 
        transport.sendMail({
          from,
          to: recipient,
          subject,
          html,
          text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML if text version not provided
        })
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Wait before sending the next batch
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, BATCH_DELAY_MS));
      }
    }
    
    return { success: true, results };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
}