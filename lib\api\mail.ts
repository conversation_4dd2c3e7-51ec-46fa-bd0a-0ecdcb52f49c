import connectDB from "@/lib/database/connection";
import { Campaign, Recipient } from "@/lib/database/models";
import { Resend } from "resend";

interface SendEmailProps {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  text?: string;
  campaignId?: string;
}

interface BulkEmailResult {
  success: boolean;
  results?: any[];
  error?: any;
  totalSent?: number;
  totalFailed?: number;
  failedEmails?: string[];
}

const resend = new Resend(process.env.RESEND_API_KEY);

export async function sendEmail({
  to,
  subject,
  html,
  from = process.env.EMAIL_FROM || "<EMAIL>",
  text,
  campaignId,
}: SendEmailProps): Promise<BulkEmailResult> {
  try {
    await connectDB();

    const recipients = Array.isArray(to) ? to : [to];

    // Get rate limiting settings from environment
    const MAX_EMAILS_PER_BATCH =
      Number(process.env.MAX_EMAILS_PER_BATCH) || 100;
    const BATCH_DELAY_MS = Number(process.env.BATCH_DELAY_MS) || 1000;

    // Split recipients into batches
    const batches = [];
    for (let i = 0; i < recipients.length; i += MAX_EMAILS_PER_BATCH) {
      batches.push(recipients.slice(i, i + MAX_EMAILS_PER_BATCH));
    }

    // Send emails in batches
    const results = [];
    const failedEmails: string[] = [];
    let totalSent = 0;
    let totalFailed = 0;

    for (const batch of batches) {
      const batchPromises = batch.map(async (recipient) => {
        try {
          // Add tracking pixels and unsubscribe links if campaignId is provided
          let enhancedHtml = html;
          if (campaignId) {
            enhancedHtml = addTrackingToEmail(html, campaignId, recipient);
          }

          const result = await resend.emails.send({
            from,
            to: recipient,
            subject,
            html: enhancedHtml,
            text: text || html.replace(/<[^>]*>/g, ""), // Strip HTML if text version not provided
          });

          totalSent++;

          // Update recipient stats if campaignId is provided
          if (campaignId) {
            await updateRecipientStats(recipient, "sent");
          }

          return { success: true, recipient, result };
        } catch (error) {
          console.error(`Failed to send email to ${recipient}:`, error);
          failedEmails.push(recipient);
          totalFailed++;
          return { success: false, recipient, error };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Update campaign stats if campaignId is provided
      if (campaignId) {
        await updateCampaignStats(campaignId, totalSent, totalFailed);
      }

      // Wait before sending the next batch
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, BATCH_DELAY_MS));
      }
    }

    return {
      success: true,
      results,
      totalSent,
      totalFailed,
      failedEmails: failedEmails.length > 0 ? failedEmails : undefined,
    };
  } catch (error) {
    console.error("Error sending email:", error);
    return { success: false, error };
  }
}

function addTrackingToEmail(
  html: string,
  campaignId: string,
  recipientEmail: string
): string {
  // Add tracking pixel for open tracking
  const trackingPixel = `<img src="${
    process.env.NEXTAUTH_URL || "http://localhost:3000"
  }/api/track/open?c=${campaignId}&r=${encodeURIComponent(
    recipientEmail
  )}" width="1" height="1" style="display:none;" />`;

  // Add unsubscribe link
  const unsubscribeLink = `${
    process.env.NEXTAUTH_URL || "http://localhost:3000"
  }/unsubscribe?c=${campaignId}&r=${encodeURIComponent(recipientEmail)}`;
  const unsubscribeHtml = `<div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;"><a href="${unsubscribeLink}" style="color: #666;">Unsubscribe</a></div>`;

  // Insert tracking pixel before closing body tag
  let enhancedHtml = html.replace("</body>", `${trackingPixel}</body>`);

  // If no body tag, append at the end
  if (!enhancedHtml.includes("</body>")) {
    enhancedHtml += trackingPixel;
  }

  // Add unsubscribe link before closing body tag or at the end
  enhancedHtml = enhancedHtml.replace("</body>", `${unsubscribeHtml}</body>`);
  if (!enhancedHtml.includes("</body>")) {
    enhancedHtml += unsubscribeHtml;
  }

  return enhancedHtml;
}

async function updateRecipientStats(
  email: string,
  action: "sent" | "opened" | "clicked"
): Promise<void> {
  try {
    const updateField =
      action === "sent"
        ? "emailsSent"
        : action === "opened"
        ? "emailsOpened"
        : "emailsClicked";

    await Recipient.findOneAndUpdate(
      { email },
      {
        $inc: { [updateField]: 1 },
        $set: { lastEmailSent: action === "sent" ? new Date() : undefined },
      }
    );
  } catch (error) {
    console.error("Error updating recipient stats:", error);
  }
}

async function updateCampaignStats(
  campaignId: string,
  sent: number,
  failed: number
): Promise<void> {
  try {
    await Campaign.findByIdAndUpdate(campaignId, {
      $inc: {
        sentCount: sent,
        failedCount: failed,
      },
    });
  } catch (error) {
    console.error("Error updating campaign stats:", error);
  }
}
