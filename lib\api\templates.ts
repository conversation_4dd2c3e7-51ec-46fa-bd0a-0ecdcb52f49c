// This would typically connect to a database
// For this example, we'll use a simple in-memory store

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  thumbnail?: string;
}

// Sample templates - in a real app, these would be stored in a database
const templates: EmailTemplate[] = [
  {
    id: "template-1",
    name: "Newsletter",
    description: "Standard newsletter template with header and multiple sections",
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>Welcome to Our Newsletter</h1>
          <p>Stay updated with our latest news and offers</p>
        </div>
        
        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Header Image Would Go Here</p>
        </div>
        
        <div style="padding: 20px;">
          <h2>Latest Updates</h2>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON>am euismod metus ut enim volutpat, at cursus massa eleifend.</p>
        </div>
        
        <div style="text-align: center; padding: 20px; background-color: #f8f8f8;">
          <h3>Special Offer</h3>
          <p>25% Off All Products - Use code NEWSLETTER25 at checkout</p>
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Shop Now</a>
        </div>
        
        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> • 
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    createdAt: "2023-01-15T12:00:00Z",
    updatedAt: "2023-01-15T12:00:00Z",
    thumbnail: "https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
  {
    id: "template-2",
    name: "Product Announcement",
    description: "Showcase your product with this elegant announcement template",
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>Introducing Our New Product</h1>
          <p>We're excited to share our latest innovation with you</p>
        </div>
        
        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Product Image Would Go Here</p>
        </div>
        
        <div style="padding: 20px;">
          <h2>Meet [Product Name]</h2>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.</p>
          <ul>
            <li>Feature 1: Description of amazing feature</li>
            <li>Feature 2: Another great capability</li>
            <li>Feature 3: Something else impressive</li>
          </ul>
        </div>
        
        <div style="text-align: center; padding: 20px;">
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Learn More</a>
        </div>
        
        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> • 
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    createdAt: "2023-02-10T10:30:00Z",
    updatedAt: "2023-02-10T10:30:00Z",
    thumbnail: "https://images.pexels.com/photos/821754/pexels-photo-821754.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
  {
    id: "template-3",
    name: "Event Invitation",
    description: "Invite recipients to your event with this attractive template",
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>You're Invited!</h1>
          <p>Join us for a special event</p>
        </div>
        
        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Event Image Would Go Here</p>
        </div>
        
        <div style="padding: 20px; text-align: center;">
          <h2>[Event Name]</h2>
          <p style="font-size: 18px;">[Date & Time]</p>
          <p style="font-size: 16px;">[Location]</p>
          <p style="margin-top: 20px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.</p>
        </div>
        
        <div style="text-align: center; padding: 20px;">
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">RSVP Now</a>
        </div>
        
        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> • 
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    createdAt: "2023-03-05T15:45:00Z",
    updatedAt: "2023-03-05T15:45:00Z",
    thumbnail: "https://images.pexels.com/photos/7149165/pexels-photo-7149165.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
];

export async function getTemplates() {
  // In a real app, this would fetch from a database
  return templates;
}

export async function getTemplateById(id: string) {
  // In a real app, this would fetch from a database
  const template = templates.find(t => t.id === id);
  if (!template) return null;
  
  return template;
}

export async function createTemplate(template: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt'>) {
  // In a real app, this would save to a database
  const newTemplate: EmailTemplate = {
    ...template,
    id: `template-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  templates.push(newTemplate);
  return newTemplate;
}

export async function updateTemplate(id: string, template: Partial<EmailTemplate>) {
  // In a real app, this would update in a database
  const index = templates.findIndex(t => t.id === id);
  if (index === -1) return null;
  
  const updatedTemplate = {
    ...templates[index],
    ...template,
    updatedAt: new Date().toISOString(),
  };
  
  templates[index] = updatedTemplate;
  return updatedTemplate;
}

export async function deleteTemplate(id: string) {
  // In a real app, this would delete from a database
  const index = templates.findIndex(t => t.id === id);
  if (index === -1) return false;
  
  templates.splice(index, 1);
  return true;
}