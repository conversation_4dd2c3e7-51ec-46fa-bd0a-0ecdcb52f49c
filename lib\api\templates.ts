import connectDB from "@/lib/database/connection";
import { Template, type ITemplate } from "@/lib/database/models";

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  thumbnail?: string;
  category?: string;
  tags?: string[];
}

// Sample templates for initial seeding
const sampleTemplates: Omit<ITemplate, "_id" | "createdAt" | "updatedAt">[] = [
  {
    name: "Newsletter",
    description:
      "Standard newsletter template with header and multiple sections",
    category: "newsletter",
    isPublic: true,
    usageCount: 0,
    tags: ["newsletter", "standard"],
    variables: [],
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>Welcome to Our Newsletter</h1>
          <p>Stay updated with our latest news and offers</p>
        </div>

        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Header Image Would Go Here</p>
        </div>

        <div style="padding: 20px;">
          <h2>Latest Updates</h2>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.</p>
        </div>

        <div style="text-align: center; padding: 20px; background-color: #f8f8f8;">
          <h3>Special Offer</h3>
          <p>25% Off All Products - Use code NEWSLETTER25 at checkout</p>
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Shop Now</a>
        </div>

        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> •
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    createdAt: "2023-01-15T12:00:00Z",
    updatedAt: "2023-01-15T12:00:00Z",
    thumbnail:
      "https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
  {
    id: "template-2",
    name: "Product Announcement",
    description:
      "Showcase your product with this elegant announcement template",
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>Introducing Our New Product</h1>
          <p>We're excited to share our latest innovation with you</p>
        </div>

        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Product Image Would Go Here</p>
        </div>

        <div style="padding: 20px;">
          <h2>Meet [Product Name]</h2>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.</p>
          <ul>
            <li>Feature 1: Description of amazing feature</li>
            <li>Feature 2: Another great capability</li>
            <li>Feature 3: Something else impressive</li>
          </ul>
        </div>

        <div style="text-align: center; padding: 20px;">
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Learn More</a>
        </div>

        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> •
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    category: "promotional",
    isPublic: true,
    usageCount: 0,
    tags: ["product", "announcement"],
    variables: [],
    thumbnail:
      "https://images.pexels.com/photos/821754/pexels-photo-821754.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
  {
    name: "Event Invitation",
    description:
      "Invite recipients to your event with this attractive template",
    category: "announcement",
    isPublic: true,
    usageCount: 0,
    tags: ["event", "invitation"],
    variables: [],
    content: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 20px;">
          <h1>You're Invited!</h1>
          <p>Join us for a special event</p>
        </div>

        <div style="background-color: #f0f0f0; padding: 20px; text-align: center;">
          <p>Event Image Would Go Here</p>
        </div>

        <div style="padding: 20px; text-align: center;">
          <h2>[Event Name]</h2>
          <p style="font-size: 18px;">[Date & Time]</p>
          <p style="font-size: 16px;">[Location]</p>
          <p style="margin-top: 20px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod metus ut enim volutpat, at cursus massa eleifend.</p>
        </div>

        <div style="text-align: center; padding: 20px;">
          <a href="#" style="display: inline-block; background-color: #4a7bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">RSVP Now</a>
        </div>

        <div style="text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
          <p>© 2023 Your Company. All rights reserved.</p>
          <div>
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Unsubscribe</a> •
            <a href="#" style="color: #4a7bff; text-decoration: underline;">Privacy Policy</a>
          </div>
        </div>
      </div>
    `,
    thumbnail:
      "https://images.pexels.com/photos/7149165/pexels-photo-7149165.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
  },
];

export async function getTemplates() {
  try {
    await connectDB();
    const templates = await Template.find({ isPublic: true })
      .sort({ createdAt: -1 })
      .lean();
    return templates;
  } catch (error) {
    console.error("Error fetching templates:", error);
    return [];
  }
}

export async function getTemplateById(id: string) {
  try {
    await connectDB();
    const template = await Template.findById(id).lean();
    return template;
  } catch (error) {
    console.error("Error fetching template:", error);
    return null;
  }
}

export async function createTemplate(
  templateData: Omit<ITemplate, "_id" | "createdAt" | "updatedAt">
) {
  try {
    await connectDB();
    const template = await Template.create(templateData);
    return template;
  } catch (error) {
    console.error("Error creating template:", error);
    throw error;
  }
}

export async function updateTemplate(id: string, updates: Partial<ITemplate>) {
  try {
    await connectDB();
    const template = await Template.findByIdAndUpdate(id, updates, {
      new: true,
    }).lean();
    return template;
  } catch (error) {
    console.error("Error updating template:", error);
    return null;
  }
}

export async function deleteTemplate(id: string) {
  try {
    await connectDB();
    const result = await Template.findByIdAndDelete(id);
    return !!result;
  } catch (error) {
    console.error("Error deleting template:", error);
    return false;
  }
}

export async function seedTemplates() {
  try {
    await connectDB();

    // Check if templates already exist
    const existingCount = await Template.countDocuments();
    if (existingCount > 0) {
      return; // Templates already seeded
    }

    // Create sample templates
    await Template.insertMany(sampleTemplates);
    console.log("Sample templates seeded successfully");
  } catch (error) {
    console.error("Error seeding templates:", error);
  }
}
