import mongoose, { Document, Schema } from 'mongoose';

export interface ICampaign extends Document {
  _id: string;
  name: string;
  subject: string;
  content: string;
  templateId?: string;
  senderName: string;
  senderEmail: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused' | 'failed';
  scheduledAt?: Date;
  sentAt?: Date;
  recipients: string[]; // Array of recipient IDs
  recipientLists: string[]; // Array of list IDs
  totalRecipients: number;
  sentCount: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
  bouncedCount: number;
  unsubscribedCount: number;
  failedCount: number;
  analytics: {
    opens: Array<{
      recipientId: string;
      timestamp: Date;
      userAgent?: string;
      ipAddress?: string;
    }>;
    clicks: Array<{
      recipientId: string;
      url: string;
      timestamp: Date;
      userAgent?: string;
      ipAddress?: string;
    }>;
    bounces: Array<{
      recipientId: string;
      reason: string;
      timestamp: Date;
    }>;
    unsubscribes: Array<{
      recipientId: string;
      timestamp: Date;
    }>;
  };
  settings: {
    trackOpens: boolean;
    trackClicks: boolean;
    unsubscribeLink: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const CampaignSchema = new Schema<ICampaign>({
  name: { type: String, required: true },
  subject: { type: String, required: true },
  content: { type: String, required: true },
  templateId: { type: String },
  senderName: { type: String, required: true },
  senderEmail: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['draft', 'scheduled', 'sending', 'sent', 'paused', 'failed'],
    default: 'draft'
  },
  scheduledAt: { type: Date },
  sentAt: { type: Date },
  recipients: [{ type: String }],
  recipientLists: [{ type: String }],
  totalRecipients: { type: Number, default: 0 },
  sentCount: { type: Number, default: 0 },
  deliveredCount: { type: Number, default: 0 },
  openedCount: { type: Number, default: 0 },
  clickedCount: { type: Number, default: 0 },
  bouncedCount: { type: Number, default: 0 },
  unsubscribedCount: { type: Number, default: 0 },
  failedCount: { type: Number, default: 0 },
  analytics: {
    opens: [{
      recipientId: String,
      timestamp: Date,
      userAgent: String,
      ipAddress: String
    }],
    clicks: [{
      recipientId: String,
      url: String,
      timestamp: Date,
      userAgent: String,
      ipAddress: String
    }],
    bounces: [{
      recipientId: String,
      reason: String,
      timestamp: Date
    }],
    unsubscribes: [{
      recipientId: String,
      timestamp: Date
    }]
  },
  settings: {
    trackOpens: { type: Boolean, default: true },
    trackClicks: { type: Boolean, default: true },
    unsubscribeLink: { type: Boolean, default: true }
  }
}, {
  timestamps: true
});

// Indexes for better performance
CampaignSchema.index({ status: 1 });
CampaignSchema.index({ createdAt: -1 });
CampaignSchema.index({ senderEmail: 1 });

export default mongoose.models.Campaign || mongoose.model<ICampaign>('Campaign', CampaignSchema);
