import mongoose, { Document, Schema } from "mongoose";

export interface IRecipient extends Document {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  status: "active" | "unsubscribed" | "bounced" | "complained";
  customFields: Record<string, any>;
  lists: string[]; // Array of list IDs
  subscriptionDate: Date;
  unsubscribeDate?: Date;
  bounceReason?: string;
  lastEmailSent?: Date;
  emailsSent: number;
  emailsOpened: number;
  emailsClicked: number;
  tags: string[];
  source?: string; // How they were added (import, manual, api, etc.)
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  updatedAt: Date;
}

const RecipientSchema = new Schema<IRecipient>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      validate: {
        validator: function (email: string) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        },
        message: "Invalid email format",
      },
    },
    firstName: { type: String, trim: true },
    lastName: { type: String, trim: true },
    name: { type: String, trim: true },
    status: {
      type: String,
      enum: ["active", "unsubscribed", "bounced", "complained"],
      default: "active",
    },
    customFields: { type: Schema.Types.Mixed, default: {} },
    lists: [{ type: Schema.Types.ObjectId, ref: "RecipientList" }],
    subscriptionDate: { type: Date, default: Date.now },
    unsubscribeDate: { type: Date },
    bounceReason: { type: String },
    lastEmailSent: { type: Date },
    emailsSent: { type: Number, default: 0 },
    emailsOpened: { type: Number, default: 0 },
    emailsClicked: { type: Number, default: 0 },
    tags: [{ type: String }],
    source: { type: String },
    ipAddress: { type: String },
    userAgent: { type: String },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance (email index is already created by unique: true)
RecipientSchema.index({ status: 1 });
RecipientSchema.index({ lists: 1 });
RecipientSchema.index({ tags: 1 });
RecipientSchema.index({ createdAt: -1 });

// Virtual for full name
RecipientSchema.virtual("fullName").get(function () {
  if (this.name) return this.name;
  if (this.firstName && this.lastName)
    return `${this.firstName} ${this.lastName}`;
  if (this.firstName) return this.firstName;
  return this.email.split("@")[0];
});

export default mongoose.models.Recipient ||
  mongoose.model<IRecipient>("Recipient", RecipientSchema);
