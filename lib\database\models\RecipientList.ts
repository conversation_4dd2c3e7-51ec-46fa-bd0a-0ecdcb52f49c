import mongoose, { Document, Schema } from 'mongoose';

export interface IRecipientList extends Document {
  _id: string;
  name: string;
  description?: string;
  tags: string[];
  recipientCount: number;
  activeRecipientCount: number;
  isDefault: boolean;
  createdBy?: string; // User ID who created the list
  settings: {
    allowDuplicates: boolean;
    autoCleanBounces: boolean;
    autoCleanUnsubscribes: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const RecipientListSchema = new Schema<IRecipientList>({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { type: String, trim: true },
  tags: [{ type: String }],
  recipientCount: { type: Number, default: 0 },
  activeRecipientCount: { type: Number, default: 0 },
  isDefault: { type: Boolean, default: false },
  createdBy: { type: String },
  settings: {
    allowDuplicates: { type: Boolean, default: false },
    autoCleanBounces: { type: Boolean, default: true },
    autoCleanUnsubscribes: { type: Boolean, default: true }
  }
}, {
  timestamps: true
});

// Indexes for better performance
RecipientListSchema.index({ name: 1 });
RecipientListSchema.index({ tags: 1 });
RecipientListSchema.index({ createdAt: -1 });

export default mongoose.models.RecipientList || mongoose.model<IRecipientList>('RecipientList', RecipientListSchema);
