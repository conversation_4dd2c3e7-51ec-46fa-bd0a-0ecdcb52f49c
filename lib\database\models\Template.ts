import mongoose, { Document, Schema } from 'mongoose';

export interface ITemplate extends Document {
  _id: string;
  name: string;
  description?: string;
  content: string;
  thumbnail?: string;
  category: string;
  isPublic: boolean;
  createdBy?: string; // User ID who created the template
  usageCount: number;
  tags: string[];
  variables: Array<{
    name: string;
    type: 'text' | 'number' | 'date' | 'boolean' | 'url';
    defaultValue?: string;
    required: boolean;
    description?: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const TemplateSchema = new Schema<ITemplate>({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { type: String, trim: true },
  content: { type: String, required: true },
  thumbnail: { type: String },
  category: { 
    type: String, 
    default: 'general',
    enum: ['newsletter', 'promotional', 'transactional', 'announcement', 'general']
  },
  isPublic: { type: Boolean, default: true },
  createdBy: { type: String },
  usageCount: { type: Number, default: 0 },
  tags: [{ type: String }],
  variables: [{
    name: { type: String, required: true },
    type: { 
      type: String, 
      enum: ['text', 'number', 'date', 'boolean', 'url'],
      default: 'text'
    },
    defaultValue: { type: String },
    required: { type: Boolean, default: false },
    description: { type: String }
  }]
}, {
  timestamps: true
});

// Indexes for better performance
TemplateSchema.index({ name: 1 });
TemplateSchema.index({ category: 1 });
TemplateSchema.index({ tags: 1 });
TemplateSchema.index({ isPublic: 1 });
TemplateSchema.index({ createdAt: -1 });

export default mongoose.models.Template || mongoose.model<ITemplate>('Template', TemplateSchema);
