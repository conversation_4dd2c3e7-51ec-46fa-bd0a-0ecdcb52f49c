import connectDB from './connection';
import { Campaign, Recipient, RecipientList, Template } from './models';
import { seedTemplates } from '../api/templates';

export async function seedDatabase() {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // Seed templates
    await seedTemplates();

    // Create sample recipient lists
    const existingLists = await RecipientList.countDocuments();
    if (existingLists === 0) {
      const sampleLists = [
        {
          name: 'Newsletter Subscribers',
          description: 'Users who subscribed to our newsletter',
          tags: ['newsletter', 'subscribers'],
          recipientCount: 0,
          activeRecipientCount: 0,
          settings: {
            allowDuplicates: false,
            autoCleanBounces: true,
            autoCleanUnsubscribes: true
          }
        },
        {
          name: 'Active Customers',
          description: 'Customers who made a purchase in the last 6 months',
          tags: ['customers', 'active'],
          recipientCount: 0,
          activeRecipientCount: 0,
          settings: {
            allowDuplicates: false,
            autoCleanBounces: true,
            autoCleanUnsubscribes: true
          }
        },
        {
          name: 'Event Attendees',
          description: 'People who attended our events',
          tags: ['events', 'attendees'],
          recipientCount: 0,
          activeRecipientCount: 0,
          settings: {
            allowDuplicates: false,
            autoCleanBounces: true,
            autoCleanUnsubscribes: true
          }
        }
      ];

      const createdLists = await RecipientList.insertMany(sampleLists);
      console.log('Sample recipient lists created');

      // Create sample recipients
      const sampleRecipients = [
        {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          name: 'John Doe',
          status: 'active',
          lists: [createdLists[0]._id, createdLists[1]._id],
          source: 'manual',
          customFields: {
            company: 'Tech Corp',
            position: 'Developer'
          }
        },
        {
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          name: 'Jane Smith',
          status: 'active',
          lists: [createdLists[0]._id],
          source: 'manual',
          customFields: {
            company: 'Design Studio',
            position: 'Designer'
          }
        },
        {
          email: '<EMAIL>',
          firstName: 'Bob',
          lastName: 'Johnson',
          name: 'Bob Johnson',
          status: 'active',
          lists: [createdLists[2]._id],
          source: 'manual',
          customFields: {
            company: 'Marketing Inc',
            position: 'Manager'
          }
        },
        {
          email: '<EMAIL>',
          firstName: 'Sarah',
          lastName: 'Williams',
          name: 'Sarah Williams',
          status: 'active',
          lists: [createdLists[0]._id, createdLists[1]._id, createdLists[2]._id],
          source: 'manual',
          customFields: {
            company: 'Startup LLC',
            position: 'CEO'
          }
        }
      ];

      await Recipient.insertMany(sampleRecipients);
      console.log('Sample recipients created');

      // Update list counts
      for (const list of createdLists) {
        const count = await Recipient.countDocuments({ lists: list._id, status: 'active' });
        await RecipientList.findByIdAndUpdate(list._id, {
          recipientCount: count,
          activeRecipientCount: count
        });
      }
      console.log('Recipient list counts updated');
    }

    // Create sample campaigns
    const existingCampaigns = await Campaign.countDocuments();
    if (existingCampaigns === 0) {
      const templates = await Template.find().limit(2);
      const lists = await RecipientList.find().limit(2);

      const sampleCampaigns = [
        {
          name: 'Weekly Newsletter',
          subject: 'Your Weekly Update - Important News Inside',
          content: templates[0]?.content || '<h1>Welcome to our newsletter!</h1><p>This is a sample newsletter content.</p>',
          templateId: templates[0]?._id,
          senderName: 'Newsletter Team',
          senderEmail: '<EMAIL>',
          status: 'sent',
          recipientLists: [lists[0]?._id],
          totalRecipients: 2,
          sentCount: 2,
          deliveredCount: 2,
          openedCount: 1,
          clickedCount: 0,
          sentAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          settings: {
            trackOpens: true,
            trackClicks: true,
            unsubscribeLink: true
          },
          analytics: {
            opens: [],
            clicks: [],
            bounces: [],
            unsubscribes: []
          }
        },
        {
          name: 'Product Announcement',
          subject: 'Exciting New Product Launch!',
          content: templates[1]?.content || '<h1>New Product Launch!</h1><p>We are excited to announce our new product.</p>',
          templateId: templates[1]?._id,
          senderName: 'Product Team',
          senderEmail: '<EMAIL>',
          status: 'draft',
          recipientLists: [lists[1]?._id],
          totalRecipients: 1,
          settings: {
            trackOpens: true,
            trackClicks: true,
            unsubscribeLink: true
          },
          analytics: {
            opens: [],
            clicks: [],
            bounces: [],
            unsubscribes: []
          }
        }
      ];

      await Campaign.insertMany(sampleCampaigns);
      console.log('Sample campaigns created');
    }

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
