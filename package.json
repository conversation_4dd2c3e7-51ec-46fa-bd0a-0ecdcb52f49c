{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx lib/database/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@types/multer": "^1.4.12", "@types/node": "20.6.2", "@types/nodemailer": "^6.4.14", "@types/papaparse": "^5.3.16", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parser": "^3.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "input-otp": "^1.4.2", "lucide-react": "^0.446.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "multer": "^2.0.0", "next": "13.5.1", "next-themes": "^0.3.0", "nodemailer": "^6.9.8", "papaparse": "^5.5.3", "postcss": "8.4.30", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "tsx": "^4.6.2", "typescript": "5.2.2", "vaul": "^0.9.9", "zod": "^3.25.32"}}